<?php
/**
 * AJAX handlers class for WP Git Manager
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WPGitManager_AjaxHandlers {
    
    private $git_ops;
    
    public function __construct() {
        include_once plugin_dir_path(__FILE__) . 'class-git-operations.php';
        $this->git_ops = new WPGitManager_GitOperations();
    }
    
    public function handle_commit() {
        check_ajax_referer('wp_git_manager_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }
        
        $commit_message = sanitize_text_field($_POST['message']);
        
        if (empty($commit_message)) {
            wp_send_json_error('Commit message is required');
        }
        
        // Add all files
        $add_result = $this->git_ops->execute_command('add -A');
        if (!$add_result['success']) {
            wp_send_json_error('Failed to add files: ' . $add_result['message']);
        }
        
        // Commit changes
        $commit_result = $this->git_ops->execute_command('commit -m ' . escapeshellarg($commit_message));
        if ($commit_result['success'] || strpos($commit_result['message'], 'nothing to commit') !== false) {
            wp_send_json_success($commit_result['message']);
        } else {
            wp_send_json_error('Commit failed: ' . $commit_result['message']);
        }
    }
    
    public function handle_status() {
        check_ajax_referer('wp_git_manager_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }
        
        $status = $this->git_ops->get_status();
        wp_send_json_success($status);
    }
    
    public function handle_push() {
        check_ajax_referer('wp_git_manager_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }
        
        $remote_name = get_option('wpgm_remote_name', 'origin');
        $branch_name = get_option('wpgm_branch_name', 'main');
        
        $push_result = $this->git_ops->execute_command('push ' . escapeshellarg($remote_name) . ' ' . escapeshellarg($branch_name));
        
        if ($push_result['success']) {
            wp_send_json_success($push_result['message']);
        } else {
            wp_send_json_error('Push failed: ' . $push_result['message']);
        }
    }
    
    public function handle_pull() {
        check_ajax_referer('wp_git_manager_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }
        
        $remote_name = get_option('wpgm_remote_name', 'origin');
        $branch_name = get_option('wpgm_branch_name', 'main');
        
        $pull_result = $this->git_ops->execute_command('pull ' . escapeshellarg($remote_name) . ' ' . escapeshellarg($branch_name));
        
        if ($pull_result['success']) {
            wp_send_json_success($pull_result['message']);
        } else {
            wp_send_json_error('Pull failed: ' . $pull_result['message']);
        }
    }
    
    public function handle_setup_check() {
        check_ajax_referer('wp_git_manager_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }
        
        $check_type = sanitize_text_field($_POST['check_type']);
        
        switch ($check_type) {
            case 'git_path':
                $git_path = sanitize_text_field($_POST['git_path']);
                $result = $this->git_ops->test_git_path($git_path);
                
                if ($result['success']) {
                    wp_send_json_success($result);
                } else {
                    wp_send_json_error($result['message']);
                }
                break;
                
            case 'full_status':
                $git_path = sanitize_text_field($_POST['git_path']);
                $repo_path = sanitize_text_field($_POST['repo_path']);
                
                // Save paths for future use
                update_option('wpgm_git_path', $git_path);
                update_option('wpgm_repo_path', $repo_path);
                
                $status = $this->git_ops->check_setup_status();
                wp_send_json_success($status);
                break;
                
            case 'save_settings':
                $settings = $_POST['settings'];
                foreach ($settings as $key => $value) {
                    update_option('wpgm_' . $key, sanitize_text_field($value));
                }
                update_option('wpgm_needs_setup', false);
                wp_send_json_success('Settings saved');
                break;
        }
        
        wp_send_json_error('Invalid check type');
    }
    
    public function handle_init_repo() {
        check_ajax_referer('wp_git_manager_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }
        
        $git_path = sanitize_text_field($_POST['git_path']);
        $repo_path = sanitize_text_field($_POST['repo_path']);
        
        $result = $this->git_ops->init_repository($git_path, $repo_path);
        
        if ($result['success']) {
            wp_send_json_success($result['message']);
        } else {
            wp_send_json_error($result['message']);
        }
    }
    
    public function handle_set_user() {
        check_ajax_referer('wp_git_manager_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }
        
        $git_path = sanitize_text_field($_POST['git_path']);
        $repo_path = sanitize_text_field($_POST['repo_path']);
        $user_name = sanitize_text_field($_POST['user_name']);
        $user_email = sanitize_email($_POST['user_email']);
        
        $result = $this->git_ops->set_user_config($git_path, $repo_path, $user_name, $user_email);
        
        if ($result['success']) {
            wp_send_json_success($result['message']);
        } else {
            wp_send_json_error($result['message']);
        }
    }
    
    public function handle_add_remote() {
        check_ajax_referer('wp_git_manager_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }
        
        $git_path = sanitize_text_field($_POST['git_path']);
        $repo_path = sanitize_text_field($_POST['repo_path']);
        $remote_url = esc_url_raw($_POST['remote_url']);
        $remote_name = sanitize_text_field($_POST['remote_name']);
        
        $result = $this->git_ops->add_remote($git_path, $repo_path, $remote_name, $remote_url);
        
        if ($result['success']) {
            wp_send_json_success($result['message']);
        } else {
            wp_send_json_error($result['message']);
        }
    }
    
    public function handle_create_branch() {
        check_ajax_referer('wp_git_manager_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }
        
        $git_path = sanitize_text_field($_POST['git_path']);
        $repo_path = sanitize_text_field($_POST['repo_path']);
        $branch_name = sanitize_text_field($_POST['branch_name']);
        
        if (empty($branch_name)) {
            wp_send_json_error('Branch name is required');
        }
        
        $result = $this->git_ops->create_branch($git_path, $repo_path, $branch_name);
        
        if ($result['success']) {
            wp_send_json_success($result['message']);
        } else {
            wp_send_json_error($result['message']);
        }
    }
    
    public function handle_git_command() {
        check_ajax_referer('wp_git_manager_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $git_command = sanitize_text_field($_POST['git_command']);
        $result = $this->git_ops->execute_command($git_command);

        if ($result['success']) {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result['message']);
        }
    }

    // Enhanced Git features handlers

    public function handle_stage_file()
    {
        check_ajax_referer('wp_git_manager_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $file_path = sanitize_text_field($_POST['file']);
        $result = $this->git_ops->stage_file($file_path);

        if ($result['success']) {
            wp_send_json_success($result['message']);
        } else {
            wp_send_json_error($result['message']);
        }
    }

    public function handle_unstage_file()
    {
        check_ajax_referer('wp_git_manager_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $file_path = sanitize_text_field($_POST['file']);
        $result = $this->git_ops->unstage_file($file_path);

        if ($result['success']) {
            wp_send_json_success($result['message']);
        } else {
            wp_send_json_error($result['message']);
        }
    }

    public function handle_file_diff()
    {
        check_ajax_referer('wp_git_manager_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $file_path = sanitize_text_field($_POST['file']);
        $result = $this->git_ops->get_file_diff($file_path);

        if ($result['success']) {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result['message']);
        }
    }

    public function handle_commit_history()
    {
        check_ajax_referer('wp_git_manager_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $limit = isset($_POST['limit']) ? intval($_POST['limit']) : 10;
        $result = $this->git_ops->get_commit_history($limit);

        if ($result['success']) {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result['message']);
        }
    }

    public function handle_switch_branch()
    {
        check_ajax_referer('wp_git_manager_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $branch_name = sanitize_text_field($_POST['branch']);
        $result = $this->git_ops->switch_branch($branch_name);

        if ($result['success']) {
            wp_send_json_success($result['message']);
        } else {
            wp_send_json_error($result['message']);
        }
    }

    public function handle_get_branches()
    {
        check_ajax_referer('wp_git_manager_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $result = $this->git_ops->get_branches();

        if ($result['success']) {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result['message']);
        }
    }

    /**
     * Handle Git repository reset request
     */
    public function handle_reset_repository()
    {
        check_ajax_referer('wp_git_manager_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        // Double confirmation required for this destructive action
        $confirm = sanitize_text_field($_POST['confirm']);
        if ($confirm !== 'RESET_REPOSITORY') {
            wp_send_json_error('Confirmation required. Please type "RESET_REPOSITORY" to confirm.');
        }

        $repo_path = isset($_POST['repo_path']) ? sanitize_text_field($_POST['repo_path']) : null;
        $result = $this->git_ops->reset_repository($repo_path);

        if ($result['success']) {
            wp_send_json_success($result['message']);
        } else {
            wp_send_json_error($result['message']);
        }
    }

    /**
     * Handle repository information request
     */
    public function handle_get_repository_info()
    {
        check_ajax_referer('wp_git_manager_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $info = $this->git_ops->get_repository_info();

        // Format the size for display
        if ($info['size'] > 0) {
            $info['size_formatted'] = $this->git_ops->format_bytes($info['size']);
        } else {
            $info['size_formatted'] = '0 B';
        }

        wp_send_json_success($info);
    }
}

